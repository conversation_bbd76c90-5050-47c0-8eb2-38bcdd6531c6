#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <ctype.h>
#include <math.h>

int count_star(char *str)
{
    int i = 0;
    int j = 0;
    while(str[i] != '\0') {
        if(str[i] == '*' && str[i + 1] >= 48 && str[i + 1] <= 57)
        j++;
        i++;
    }
    return j + 1;
}

double all_num(char *av, double x)
{
    // Créer une copie car strtok modifie la chaîne
    char *copy = malloc(strlen(av) + 1);
    if(copy == NULL)
        return 0;
    strcpy(copy, av);

    int taille1 = count_star(av);
    int *tab1 = malloc(sizeof(int) * taille1);
    if(tab1 == NULL) {
        free(copy);
        return 0;
    }

    double num = 0;
    char *res1 = strtok(copy, "*");
    int i = 0;
    while(res1 != NULL) {
        tab1[i] = atoi(res1);
        //printf("res = %s\n", res1);
        res1 = strtok(NULL, "*");
        i++;
    }

    for(i = taille1 - 1; i >= 0; i--) {
        num += tab1[i]*pow(x, i);
        printf ("i = %f\n", tab1[i]*pow(x, i));
    }

    // Libérer la mémoire
    free(tab1);
    free(copy);
    return num;
}


double all_denom(char *av, double x)
{
    // Créer une copie car strtok modifie la chaîne
    char *copy = malloc(strlen(av) + 1);
    if(copy == NULL)
        return 0;
    strcpy(copy, av);

    int taille2 = count_star(av);
    int *tab2 = malloc(sizeof(int) * taille2);
    if(tab2 == NULL) {
        free(copy);
        return 0;
    }

    double d = 0;
    char *res2 = strtok(copy, "*");
    int i = 0;
    while(res2 != NULL) {
        tab2[i] = atoi(res2);
        res2 = strtok(NULL, "*");
        i++;
    }

    for(i = taille2 - 1; i >= 0; i--) {
        d += tab2[i]*pow(x, i);
    }

    // Libérer la mémoire
    free(tab2);
    free(copy);
    return d;
}





int main1(int ac, char **av)
{
    if(av[1] == NULL || strlen(av[1]) == 0)
        return 84;

    int taille1 = count_star(av[1]);
    double x = 2;
    double num = 0;
    int *tab1 = malloc(sizeof(int) * taille1);
    if(tab1 == NULL)
        return 84;

    char *str1 = malloc(strlen(av[1]) + 1);
    char *str2 = malloc(strlen(av[2]) + 1);
    if(str1 == NULL) {
        free(tab1);
        return 84;
    }
    strcpy(str1, av[1]);
    int i = 0;
    char *res1 = strtok(str1, "*");
    while(res1 != NULL) {
        tab1[i] = atoi(res1);
        res1 = strtok(NULL, "*");
        i++;
    }
int *tab2 = malloc(sizeof(int) * count_star(av[2]));
strcpy(str2, av[2]);
    int j = 0;
    char *res2 = strtok(str2, "*");
    while(res2 != NULL) {
        tab2[j] = atoi(res2);
        res2 = strtok(NULL, "*");
        j++;
    }
    int taille2 = count_star(av[2]);
int d = 0;


    for (x = 0; x <= 1.001; x += 0.001) {
        num = 0;
        for(i = taille1 - 1; i >= 0; i--) {
            num += tab1[i]*pow(x, i);
        }
        d = 0;
        for(j = taille2 - 1; j >= 0; j--) {
            d += tab2[j]*pow(x, j);
        }
            printf("%.3f -> %.5f\n", x, num/d);
    }
    free(tab1);
    free(str1);
    return 0;
}
int main(int ac, char **av)
{
    int i = 0;
    double num =1;;
    double d  = 1;
    for(i = 1; i < ac; i += 2) {
        for(double x = 0; x < 1;  x += 0.001) {
            num = 1;
             {num *= all_num(av[i], x);
             }
             d = 1;
              {d *= all_denom(av[i+1], x);
             }
             printf("%.3f -> %.5f\n", x, num/d);
        }
    }
}