#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <ctype.h>
#include <math.h>
#include <float.h>

int count_star(char *str)
{
    int i = 0;
    int j = 0;
    while(str[i] != '\0') {
        if(str[i] == '*' && str[i + 1] >= 48 && str[i + 1] <= 57)
        j++;
        i++;
    }
    return j + 1;
}

double all_num(char *av, double x)
{
    // Créer une copie car strtok modifie la chaîne
    char *copy = malloc(strlen(av) + 1);
    if(copy == NULL)
        return 0;
    strcpy(copy, av);

    int taille1 = count_star(av);
    int *tab1 = malloc(sizeof(int) * taille1);
    if(tab1 == NULL) {
        free(copy);
        return 0;
    }

    double num = 0;
    char *res1 = strtok(copy, "*");
    int i = 0;
    while(res1 != NULL) {
        tab1[i] = atoi(res1);
        //printf("res = %s\n", res1);
        res1 = strtok(NULL, "*");
        i++;
    }

    for(i = taille1 - 1; i >= 0; i--) {
        num += tab1[i]*pow(x, i);
        printf ("i = %f\n", tab1[i]*pow(x, i));
    }

    // Libérer la mémoire
    free(tab1);
    free(copy);
    return num;
}


double all_denom(char *av, double x)
{
    // Créer une copie car strtok modifie la chaîne
    char *copy = malloc(strlen(av) + 1);
    if(copy == NULL)
        return 0;
    strcpy(copy, av);

    int taille2 = count_star(av);
    int *tab2 = malloc(sizeof(int) * taille2);
    if(tab2 == NULL) {
        free(copy);
        return 0;
    }

    double d = 0;
    char *res2 = strtok(copy, "*");
    int i = 0;
    while(res2 != NULL) {
        tab2[i] = atoi(res2);
        res2 = strtok(NULL, "*");
        i++;
    }

    for(i = taille2 - 1; i >= 0; i--) {
        d += tab2[i]*pow(x, i);
    }

    // Libérer la mémoire
    free(tab2);
    free(copy);
    return d;
}



// Fonction pour valider les arguments
int validate_args(int ac, char **av)
{
    if (ac < 3) {
        printf("Usage: %s num1 den1 [num2 den2 ...]\n", av[0]);
        return 84;
    }
    if ((ac - 1) % 2 != 0) {
        
        return 84;
    }
    return 0;
}

// Fonction pour calculer le produit de toutes les fractions pour une valeur x
double calculate_product(int ac, char **av, double x)
{
    double result = 1.0;

    for (int i = 1; i < ac; i += 2) {
        double num = all_num(av[i], x);
        double den = all_denom(av[i + 1], x);

        if (den == 0) {
            return NAN; // Retourne NaN pour indiquer division par zéro
        }

        result *= (num / den);
    }

    return result;
}

// Fonction pour afficher le résultat
void print_result(double x, double result)
{
    if (isnan(result)) {
        printf("%.3f -> undefined (division by zero)\n", x);
    } else {
        printf("%.3f -> %.5f\n", x, result);
    }
}

// Fonction principale d'évaluation
void evaluate_functions(int ac, char **av)
{
    for (double x = 0; x <= 1; x += 0.001) {
        double result = calculate_product(ac, av, x);
        print_result(x, result);
    }
}

int main(int ac, char **av)
{
    if (validate_args(ac, av) != 0) {
        return 84;
    }

    evaluate_functions(ac, av);

    return 0;
}