#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <ctype.h>
#include <math.h>

int count_star(char *str)
{
    int i = 0;
    int j = 0;
    while(str[i] != '\0') {
        if(str[i] == '*' && str[i + 1] >= 48 && str[i + 1] <= 57)
        j++;
        i++;
    }
    return j + 1;
}

int main(int ac, char **av)
{
    if(av[1] == NULL || strlen(av[1]) == 0)
        return 84;

    int taille = count_star(av[1]);
    double x = 2;
    double num = 0;
    int *tab = malloc(sizeof(int) * taille);
    if(tab == NULL)
        return 84;

    char *str1 = malloc(strlen(av[1]) + 1);
    if(str1 == NULL) {
        free(tab);
        return 84;
    }
    strcpy(str1, av[1]);

    int i = 0;
    char *res1 = strtok(str1, "*");
    while(res1 != NULL) {
        tab[i] = atoi(res1);
        res1 = strtok(NULL, "*");
        i++;
    }

    // Évaluation du polynôme pour différentes valeurs de x
    for(x = 0; x <= 1; x += 0.001) {
        num = 0; // Réinitialiser num pour chaque valeur de x
        for(i = taille - 1; i >= 0; i--) {
            num += tab[i]*pow(x, i);
        }
        // Optionnel: afficher quelques valeurs pour debug
        if((int)(x * 1000) % 100 == 0) { // Affiche tous les 0.1
            printf("x = %.3f, P(x) = %f\n", x, num);
        }
    }

    printf("num = %f\n", num);

    // Libérer la mémoire
    free(tab);
    free(str1);

    return 0;
}