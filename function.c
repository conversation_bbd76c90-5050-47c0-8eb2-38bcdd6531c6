#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <ctype.h>
#include <math.h>
#include <float.h>


int usage(void)
{
    printf("USAGE\n");
    printf("    ./107transfer [num den]+\n");
    printf("DESCRIPTION\n");
    printf("    num     polynomial numerator defined by its coefficients\n");
    printf("    den     polynomial denominator defined by its coefficients\n");
    return 0;
}

int count_star(char *str)
{
    int i = 0;
    int j = 0;

    while(str[i] != '\0') {
        if(str[i] == '*' && str[i + 1] >= 48 && str[i + 1] <= 57)
        j++;
        i++;
    }
    return j + 1;
}

double all_num(char *av, double x)
{
    char *copy = malloc(strlen(av) + 1);

    if(copy == NULL)
        return 0;
    strcpy(copy, av);

    int taille1 = count_star(av);
    int *tab1 = malloc(sizeof(int) * taille1);
    if(tab1 == NULL) {
        free(copy);
        return 0;
    }

    double num = 0;
    char *res1 = strtok(copy, "*");
    int i = 0;
    while(res1 != NULL) {
        tab1[i] = atoi(res1);
        res1 = strtok(NULL, "*");
        i++;
    }

    for(i = taille1 - 1; i >= 0; i--) {
        num += tab1[i]*pow(x, i);
    }
    free(tab1);
    free(copy);
    return num;
}


double all_denom(char *av, double x)
{
    char *copy = malloc(strlen(av) + 1);

    if(copy == NULL)
        return 0;
    strcpy(copy, av);

    int taille2 = count_star(av);
    int *tab2 = malloc(sizeof(int) * taille2);
    if(tab2 == NULL) {
        free(copy);
        return 0;
    }

    double d = 0;
    char *res2 = strtok(copy, "*");
    int i = 0;
    while(res2 != NULL) {
        tab2[i] = atoi(res2);
        res2 = strtok(NULL, "*");
        i++;
    }

    for(i = taille2 - 1; i >= 0; i--) {
        d += tab2[i]*pow(x, i);
    }
    free(tab2);
    free(copy);
    return d;
}

int validate_args(int ac)
{
    if (ac < 3) {
        return 84;
    }
    if ((ac - 1) % 2 != 0) {
        return 84;
    }
    return 0;
}
int isnum(char *str)
{
    int i = 0;

    if (str[0] == '-' || str[0] == '+') {
        i = 1;
    }
    if (str[i] == '\0') {
        return 0;
    }
    while (str[i] != '\0') {
        if (str[i] < '0' || str[i] > '9') {
            return 0;
        }
        i++;
    }
    return 1;
}

int validate_coefficients(char *str)
{
    char *copy = malloc(strlen(str) + 1);
    if (copy == NULL)
        return 84;
    strcpy(copy, str);

    char *token = strtok(copy, "*");
    while (token != NULL) {
        if (!isnum(token)) {
            free(copy);
            return 84;
        }
        token = strtok(NULL, "*");
    }
    free(copy);
    return 0;
}

double calculate_product(int ac, char **av, double x)
{
    double result = 1.0;

    double num = 1;
    double den = 1;
    for (int i = 1; i < ac; i += 2) {
        num = all_num(av[i], x);
        den = all_denom(av[i + 1], x);
        if (den == 0) {
            return NAN;
        }
        result *= (num / den);
        
    }
    printf("%.3f -> %.5f\n", x, result);
}


int main(int ac, char **av)
{
    if( ac == 2 && strcmp(av[1], "-h") == 0)
        usage();
    }

    for (int i = 1; i < ac; i++) {
        if (validate_coefficients(av[i]) != 0) {
            return 84;
        }
    }

    for (double x = 0; x <= 1; x += 0.001) {
        calculate_product(ac, av, x);
    }
    return 0;
}